openapi: 3.0.3
info:
  title: CODA API
  version: 0.1.7
  description: API documentation
paths:
  /codaapi/health/:
    get:
      operationId: health_retrieve
      description: Perform a system health check
      tags:
      - health
      security:
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: ok
                  database:
                    type: string
                    example: ok
                  rabbitmq:
                    type: string
                    example: ok
                  consumers:
                    type: string
                    example: ok
          description: ''
  /codaapi/health/workers/:
    get:
      operationId: health_workers_list
      description: |-
        API endpoint that returns a read-only list of WorkerHost objects in JSON.
        For health monitoring purposes ALWAYS check the 'healthy' attribute of
        each WorkerHost.
        If the 'healthy' attribute is False, the WorkerHost is unhealthy further
        examination is needed on the 'status' and 'ping_response' attributes.
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - health
      security:
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedWorkerHostList'
          description: ''
  /codaapi/inference-jobs/:
    get:
      operationId: inference_jobs_list
      description: A ViewSet for viewing and editing ApiInferenceAsyncJob instances.
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - inference-jobs
      security:
      - cookieAuth: []
      - basicAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedApiInferenceAsyncJobList'
          description: ''
    post:
      operationId: inference_jobs_create
      description: A ViewSet for viewing and editing ApiInferenceAsyncJob instances.
      tags:
      - inference-jobs
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ApiInferenceAsyncJob'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ApiInferenceAsyncJob'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ApiInferenceAsyncJob'
        required: true
      security:
      - cookieAuth: []
      - basicAuth: []
      - {}
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiInferenceAsyncJob'
          description: ''
  /codaapi/inference-jobs/{id}/:
    get:
      operationId: inference_jobs_retrieve
      description: A ViewSet for viewing and editing ApiInferenceAsyncJob instances.
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this API Inference Async Job.
        required: true
      tags:
      - inference-jobs
      security:
      - cookieAuth: []
      - basicAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiInferenceAsyncJob'
          description: ''
    put:
      operationId: inference_jobs_update
      description: A ViewSet for viewing and editing ApiInferenceAsyncJob instances.
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this API Inference Async Job.
        required: true
      tags:
      - inference-jobs
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ApiInferenceAsyncJob'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ApiInferenceAsyncJob'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ApiInferenceAsyncJob'
        required: true
      security:
      - cookieAuth: []
      - basicAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiInferenceAsyncJob'
          description: ''
    patch:
      operationId: inference_jobs_partial_update
      description: A ViewSet for viewing and editing ApiInferenceAsyncJob instances.
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this API Inference Async Job.
        required: true
      tags:
      - inference-jobs
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedApiInferenceAsyncJob'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedApiInferenceAsyncJob'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedApiInferenceAsyncJob'
      security:
      - cookieAuth: []
      - basicAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiInferenceAsyncJob'
          description: ''
    delete:
      operationId: inference_jobs_destroy
      description: A ViewSet for viewing and editing ApiInferenceAsyncJob instances.
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this API Inference Async Job.
        required: true
      tags:
      - inference-jobs
      security:
      - cookieAuth: []
      - basicAuth: []
      - {}
      responses:
        '204':
          description: No response body
  /codaapi/service-jobs/:
    get:
      operationId: service_jobs_list
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - in: query
        name: status
        schema:
          type: string
          enum:
          - completed
          - created
          - failed
          - started
        description: |-
          * `created` - Created
          * `started` - Started
          * `completed` - Completed
          * `failed` - Failed
      - in: query
        name: type
        schema:
          type: string
          enum:
          - other
          - redflag
          - summary
          - transcription
        description: |-
          * `summary` - Summary
          * `transcription` - Transcription
          * `redflag` - Redflag
          * `other` - Other
      - in: query
        name: worker
        schema:
          type: string
          format: uuid
      - in: query
        name: workflow
        schema:
          type: string
          format: uuid
      tags:
      - service-jobs
      security:
      - cookieAuth: []
      - basicAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedServiceJobList'
          description: ''
  /codaapi/service-jobs/{id}/:
    get:
      operationId: service_jobs_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this service job.
        required: true
      tags:
      - service-jobs
      security:
      - cookieAuth: []
      - basicAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServiceJob'
          description: ''
  /codaapi/workflow-jobs/:
    get:
      operationId: workflow_jobs_list
      parameters:
      - in: query
        name: customer_id
        schema:
          type: integer
      - in: query
        name: language
        schema:
          type: string
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - in: query
        name: parley_id
        schema:
          type: integer
      - in: query
        name: status
        schema:
          type: string
          enum:
          - completed
          - created
          - failed
          - started
        description: |-
          * `created` - Created
          * `started` - Started
          * `completed` - Completed
          * `failed` - Failed
      tags:
      - workflow-jobs
      security:
      - cookieAuth: []
      - basicAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedWorkflowJobList'
          description: ''
  /codaapi/workflow-jobs/{id}/:
    get:
      operationId: workflow_jobs_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this workflow job.
        required: true
      tags:
      - workflow-jobs
      security:
      - cookieAuth: []
      - basicAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowJob'
          description: ''
components:
  schemas:
    ApiInferenceAsyncJob:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        task_id:
          type: string
          nullable: true
        input_data: {}
        generation_config:
          nullable: true
        result:
          readOnly: true
          nullable: true
        status:
          allOf:
          - $ref: '#/components/schemas/ApiInferenceAsyncJobStatusEnum'
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
          description: Model creation timestamp - automatically set by Django upon
            initial creation.
        updated_at:
          type: string
          format: date-time
          readOnly: true
          description: Model update timestamp - automatically set by Django upon any
            save operation.
        completed_at:
          type: string
          format: date-time
          readOnly: true
          nullable: true
          description: Model job completion timestamp
        failed_at:
          type: string
          format: date-time
          readOnly: true
          nullable: true
          description: Model job failure timestamp
        started_at:
          type: string
          format: date-time
          readOnly: true
          nullable: true
          description: Model job started timestamp
      required:
      - completed_at
      - created_at
      - failed_at
      - id
      - input_data
      - result
      - started_at
      - status
      - updated_at
    ApiInferenceAsyncJobStatusEnum:
      enum:
      - pending
      - completed
      - failed
      - cancelled
      type: string
      description: |-
        * `pending` - Pending
        * `completed` - Completed
        * `failed` - Failed
        * `cancelled` - Cancelled
    PaginatedApiInferenceAsyncJobList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/ApiInferenceAsyncJob'
    PaginatedServiceJobList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/ServiceJob'
    PaginatedWorkerHostList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/WorkerHost'
    PaginatedWorkflowJobList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/WorkflowJob'
    PatchedApiInferenceAsyncJob:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        task_id:
          type: string
          nullable: true
        input_data: {}
        generation_config:
          nullable: true
        result:
          readOnly: true
          nullable: true
        status:
          allOf:
          - $ref: '#/components/schemas/ApiInferenceAsyncJobStatusEnum'
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
          description: Model creation timestamp - automatically set by Django upon
            initial creation.
        updated_at:
          type: string
          format: date-time
          readOnly: true
          description: Model update timestamp - automatically set by Django upon any
            save operation.
        completed_at:
          type: string
          format: date-time
          readOnly: true
          nullable: true
          description: Model job completion timestamp
        failed_at:
          type: string
          format: date-time
          readOnly: true
          nullable: true
          description: Model job failure timestamp
        started_at:
          type: string
          format: date-time
          readOnly: true
          nullable: true
          description: Model job started timestamp
    ServiceJob:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        name:
          type: string
          readOnly: true
        type:
          allOf:
          - $ref: '#/components/schemas/TypeEnum'
          readOnly: true
        worker:
          type: string
          format: uuid
          readOnly: true
          nullable: true
        service_configuration:
          type: string
          format: uuid
          readOnly: true
          nullable: true
        workflow:
          type: string
          format: uuid
          readOnly: true
          nullable: true
        parley_id:
          type: integer
          readOnly: true
        customer_id:
          type: integer
          readOnly: true
        language:
          type: string
          readOnly: true
        status:
          allOf:
          - $ref: '#/components/schemas/StatusBb3Enum'
          readOnly: true
        error:
          type: string
          readOnly: true
          nullable: true
        transcription_retrieved_from_s3:
          type: boolean
          readOnly: true
        completed_at:
          type: string
          format: date-time
          readOnly: true
          nullable: true
          description: Model job completion timestamp
        failed_at:
          type: string
          format: date-time
          readOnly: true
          nullable: true
          description: Model job failure timestamp
      required:
      - completed_at
      - customer_id
      - error
      - failed_at
      - id
      - language
      - name
      - parley_id
      - service_configuration
      - status
      - transcription_retrieved_from_s3
      - type
      - worker
      - workflow
    StatusBb3Enum:
      enum:
      - created
      - started
      - completed
      - failed
      type: string
      description: |-
        * `created` - Created
        * `started` - Started
        * `completed` - Completed
        * `failed` - Failed
    TypeEnum:
      enum:
      - summary
      - transcription
      - redflag
      - other
      type: string
      description: |-
        * `summary` - Summary
        * `transcription` - Transcription
        * `redflag` - Redflag
        * `other` - Other
    WorkerHost:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        healthy:
          type: boolean
          readOnly: true
        ping_status:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
          description: Model creation timestamp - automatically set by Django upon
            initial creation.
        updated_at:
          type: string
          format: date-time
          readOnly: true
          description: Model update timestamp - automatically set by Django upon any
            save operation.
        hostname:
          type: string
          maxLength: 64
        ec2_type:
          type: string
          maxLength: 64
        name:
          type: string
          maxLength: 130
        description:
          type: string
          nullable: true
        status:
          $ref: '#/components/schemas/WorkerHostStatusEnum'
        down_for_maintenance:
          type: boolean
        service_configuration_bypass:
          type: string
          format: uuid
          nullable: true
        llm_model:
          type: string
          format: uuid
          nullable: true
      required:
      - created_at
      - healthy
      - hostname
      - id
      - name
      - ping_status
      - updated_at
    WorkerHostStatusEnum:
      enum:
      - running
      - stopped
      - loading
      type: string
      description: |-
        * `running` - Running
        * `stopped` - Stopped
        * `loading` - Loading
    WorkflowJob:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        parley_id:
          type: integer
          readOnly: true
        customer_id:
          type: integer
          readOnly: true
        language:
          type: string
          readOnly: true
        requested_services:
          type: string
          readOnly: true
        status:
          allOf:
          - $ref: '#/components/schemas/StatusBb3Enum'
          readOnly: true
        started_at:
          type: string
          format: date-time
          readOnly: true
          nullable: true
          description: Model job started timestamp
        completed_at:
          type: string
          format: date-time
          readOnly: true
          nullable: true
          description: Model job completion timestamp
        service_jobs:
          type: array
          items:
            $ref: '#/components/schemas/ServiceJob'
          readOnly: true
      required:
      - completed_at
      - customer_id
      - id
      - language
      - parley_id
      - requested_services
      - service_jobs
      - started_at
      - status
  securitySchemes:
    basicAuth:
      type: http
      scheme: basic
    cookieAuth:
      type: apiKey
      in: cookie
      name: sessionid
