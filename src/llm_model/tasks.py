import logging

import llm_model.services.api
import llm_model.services.redflag
import llm_model.services.summary
import llm_model.services.redflagv2
from coda.base_tasks import BaseLLMTaskWithRetry, GPUTask, BaseTaskWithRetry, RedFlagTask, ApiGPUTask, \
    BaseLLMRemoteTaskWithRetry
from coda.celery import app


logger = logging.getLogger(__name__)

use_remote_api = True

if use_remote_api:
    BaseLLMTask = BaseLLMRemoteTaskWithRetry
    # summary_queue = "default"
    # redflag_queue = "default"
else:
    BaseLLMTask = BaseLLMTaskWithRetry

summary_queue = "summary"
redflag_queue = "redflag"

default_queue = "default"


@app.task(bind=True, queue=summary_queue, base=BaseLLMTask, priority=16)
def api_task(self, *args, **kwargs):
    kwargs.update(service=llm_model.services.api.ApiService, task=self)
    task = ApiGPUTask(*args, **kwargs)
    task.run(*args, **kwargs)


@app.task(bind=True, queue=default_queue, base=BaseTaskWithRetry, priority=10)
def redflag_task(self, *args, **kwargs):
    kwargs.update(task=self, indexing_event_type="red-flag_completed")
    task = RedFlagTask(*args, **kwargs)
    task.run()


@app.task(bind=True, queue=redflag_queue, base=BaseLLMTask, priority=6)
def red_flag_analysis_task(self, *args, **kwargs):
    kwargs.update(service=llm_model.services.redflag.RedflagAnalysisService, task=self)
    task = GPUTask(*args, **kwargs)
    task.run()


@app.task(bind=True, queue=redflag_queue, base=BaseLLMTask, priority=6)
def extract_sentences_task(self, *args, **kwargs):
    kwargs.update(service=llm_model.services.redflag.RedflagExtractSentencesService, task=self)
    task = GPUTask(*args, **kwargs)
    task.run()


@app.task(bind=True, queue=redflag_queue, base=BaseLLMTask, priority=6)
def get_meta_data_task(self, *args, **kwargs):
    kwargs.update(service=llm_model.services.redflag.RedflagMetadataService, task=self)
    task = GPUTask(*args, **kwargs)
    task.run()


@app.task(bind=True, queue=redflag_queue, base=BaseLLMTask, priority=6)
def get_entities_task(self, *args, **kwargs):
    kwargs.update(service=llm_model.services.redflag.RedflagEntitiesService, task=self)
    task = GPUTask(*args, **kwargs)
    task.run()


@app.task(bind=True, queue=redflag_queue, base=BaseLLMTask, priority=6)
def get_off_channel_comms_task(self, *args, **kwargs):
    kwargs.update(service=llm_model.services.redflag.RedflagOffchannelService, task=self)
    task = GPUTask(*args, **kwargs)
    task.run()


@app.task(bind=True, queue=redflag_queue, base=BaseLLMTask, priority=6)
def get_analysis_summary_task(self, *args, **kwargs):
    kwargs.update(service=llm_model.services.redflag.RedflagAnalysisSummaryService, task=self)
    task = GPUTask(*args, **kwargs)
    task.run()


@app.task(bind=True, queue=redflag_queue, base=BaseLLMTask, priority=6)
def categorise_task(self, *args, **kwargs):
    kwargs.update(service=llm_model.services.redflag.RedflagCategoriseService, task=self)
    task = GPUTask(*args, **kwargs)
    task.run()


@app.task(bind=True, queue=summary_queue, base=BaseLLMTask, priority=15)
def summary_task(self, *args, **kwargs):
    kwargs.update(service=llm_model.services.summary.SummaryService, task=self, indexing_event_type="summarisation_completed")
    task = GPUTask(*args, **kwargs)
    task.run()

@app.task(bind=True, queue=redflag_queue, base=BaseLLMTask, priority=7)
def redflagv2_task(self, *args, **kwargs):
    kwargs.update(service=llm_model.services.redflagv2.RedflagV2Service, task=self, indexing_event_type="redflag_v2_completed")
    task = GPUTask(*args, **kwargs)
    task.run()
